<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小恐龙过桥 - 英语连词成句游戏</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- 数据上传界面 -->
    <div id="upload-screen" class="screen active">
        <div class="upload-container">
            <h1>小恐龙过桥</h1>
            <h2>英语连词成句游戏</h2>
            
            <div class="upload-section">
                <h3>添加英语短句</h3>
                
                <!-- 手动输入区域 -->
                <div class="manual-input">
                    <label for="manual-sentences">手动输入短句（每行一个）：</label>
                    <textarea id="manual-sentences" placeholder="例如：&#10;I love cats&#10;She is happy&#10;We play games"></textarea>
                    <button id="add-manual-btn">添加短句</button>
                </div>
                
                <!-- 文件上传区域 -->
                <div class="file-upload">
                    <label for="file-input">或上传txt文件：</label>
                    <input type="file" id="file-input" accept=".txt">
                    <button id="upload-btn">上传文件</button>
                </div>
            </div>
            
            <!-- 短句库预览 -->
            <div class="preview-section">
                <h3>短句库预览</h3>
                <div id="sentences-preview" class="sentences-list">
                    <p class="empty-message">暂无短句，请添加短句后开始游戏</p>
                </div>
                <button id="start-game-btn" class="start-btn" disabled>开始游戏</button>
            </div>
        </div>
    </div>
    
    <!-- 游戏界面 -->
    <div id="game-screen" class="screen">
        <div class="game-container">
            <!-- 游戏头部信息 -->
            <div class="game-header">
                <div class="score-info">
                    <span>题目: <span id="current-question">1</span>/<span id="total-questions">0</span></span>
                    <span>得分: <span id="score">0</span></span>
                </div>
                <button id="back-to-upload" class="back-btn">返回上传</button>
            </div>
            
            <!-- 游戏主区域 -->
            <div class="game-area">
                <!-- 背景层 -->
                <div class="background">
                    <div class="sky"></div>
                    <div class="clouds">
                        <div class="cloud cloud1"></div>
                        <div class="cloud cloud2"></div>
                        <div class="cloud cloud3"></div>
                    </div>
                    <div class="cliff-left"></div>
                    <div class="cliff-right"></div>
                </div>
                
                <!-- 游戏元素层 -->
                <div class="game-elements">
                    <!-- 小恐龙 -->
                    <div id="dinosaur" class="dinosaur">🦕</div>
                    
                    <!-- 桥梁区域 -->
                    <div id="bridge-area" class="bridge-area">
                        <!-- 木桩/桥板会动态生成 -->
                    </div>
                    
                    <!-- 单词选择区域 -->
                    <div id="word-selection" class="word-selection">
                        <!-- 单词按钮会动态生成 -->
                    </div>
                    
                    <!-- 当前句子显示 -->
                    <div id="current-sentence" class="current-sentence">
                        <span id="sentence-display"></span>
                    </div>
                </div>
            </div>
            
            <!-- 游戏控制按钮 -->
            <div class="game-controls">
                <button id="restart-btn">重新开始</button>
                <button id="next-question-btn" style="display: none;">下一题</button>
            </div>
        </div>
    </div>
    
    <!-- 成功弹窗 -->
    <div id="success-modal" class="modal">
        <div class="modal-content">
            <h2>Good Job! 🎉</h2>
            <p>小恐龙成功过桥了！</p>
            <button id="continue-btn">继续下一题</button>
            <button id="finish-btn">完成游戏</button>
        </div>
    </div>
    
    <!-- 失败弹窗 -->
    <div id="failure-modal" class="modal">
        <div class="modal-content">
            <h2>哎呀！😢</h2>
            <p>小恐龙掉下去了，再试一次吧！</p>
            <button id="retry-btn">重试</button>
        </div>
    </div>
    
    <!-- 音频元素 -->
    <audio id="success-sound" preload="auto">
        <source src="sounds/success.mp3" type="audio/mpeg">
    </audio>
    <audio id="failure-sound" preload="auto">
        <source src="sounds/failure.mp3" type="audio/mpeg">
    </audio>
    <audio id="click-sound" preload="auto">
        <source src="sounds/click.mp3" type="audio/mpeg">
    </audio>
    
    <script src="script.js"></script>
</body>
</html>
