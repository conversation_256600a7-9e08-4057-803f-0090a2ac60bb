// 游戏状态管理
class DinosaurBridgeGame {
    constructor() {
        this.sentences = [];
        this.currentSentenceIndex = 0;
        this.currentWordIndex = 0;
        this.score = 0;
        this.currentSentence = [];
        this.selectedWords = [];
        
        this.initializeElements();
        this.bindEvents();
        this.createDefaultSentences();
    }
    
    initializeElements() {
        // 屏幕元素
        this.uploadScreen = document.getElementById('upload-screen');
        this.gameScreen = document.getElementById('game-screen');
        
        // 上传界面元素
        this.manualSentences = document.getElementById('manual-sentences');
        this.fileInput = document.getElementById('file-input');
        this.addManualBtn = document.getElementById('add-manual-btn');
        this.uploadBtn = document.getElementById('upload-btn');
        this.sentencesPreview = document.getElementById('sentences-preview');
        this.startGameBtn = document.getElementById('start-game-btn');
        
        // 游戏界面元素
        this.currentQuestionSpan = document.getElementById('current-question');
        this.totalQuestionsSpan = document.getElementById('total-questions');
        this.scoreSpan = document.getElementById('score');
        this.dinosaur = document.getElementById('dinosaur');
        this.bridgeArea = document.getElementById('bridge-area');
        this.wordSelection = document.getElementById('word-selection');
        this.sentenceDisplay = document.getElementById('sentence-display');
        
        // 控制按钮
        this.backToUploadBtn = document.getElementById('back-to-upload');
        this.restartBtn = document.getElementById('restart-btn');
        this.nextQuestionBtn = document.getElementById('next-question-btn');
        
        // 弹窗元素
        this.successModal = document.getElementById('success-modal');
        this.failureModal = document.getElementById('failure-modal');
        this.continueBtn = document.getElementById('continue-btn');
        this.finishBtn = document.getElementById('finish-btn');
        this.retryBtn = document.getElementById('retry-btn');
        
        // 音频元素
        this.successSound = document.getElementById('success-sound');
        this.failureSound = document.getElementById('failure-sound');
        this.clickSound = document.getElementById('click-sound');
    }
    
    bindEvents() {
        // 上传界面事件
        this.addManualBtn.addEventListener('click', () => this.addManualSentences());
        this.uploadBtn.addEventListener('click', () => this.uploadFile());
        this.fileInput.addEventListener('change', () => this.handleFileSelect());
        this.startGameBtn.addEventListener('click', () => this.startGame());
        
        // 游戏界面事件
        this.backToUploadBtn.addEventListener('click', () => this.backToUpload());
        this.restartBtn.addEventListener('click', () => this.restartCurrentQuestion());
        this.nextQuestionBtn.addEventListener('click', () => this.nextQuestion());
        
        // 弹窗事件
        this.continueBtn.addEventListener('click', () => this.continueToNext());
        this.finishBtn.addEventListener('click', () => this.finishGame());
        this.retryBtn.addEventListener('click', () => this.retryQuestion());
    }
    
    createDefaultSentences() {
        const defaultSentences = [
            "I love cats",
            "She is happy",
            "We play games",
            "The sun is bright",
            "Birds can fly",
            "Dogs are friendly",
            "Children like toys",
            "Flowers smell good"
        ];
        
        this.sentences = defaultSentences;
        this.updatePreview();
    }
    
    addManualSentences() {
        const text = this.manualSentences.value.trim();
        if (!text) {
            alert('请输入一些英语短句！');
            return;
        }
        
        const newSentences = text.split('\n')
            .map(s => s.trim())
            .filter(s => s.length > 0);
        
        this.sentences = [...this.sentences, ...newSentences];
        this.manualSentences.value = '';
        this.updatePreview();
        
        this.playClickSound();
    }
    
    handleFileSelect() {
        const file = this.fileInput.files[0];
        if (file && file.type === 'text/plain') {
            this.uploadBtn.style.display = 'inline-block';
        }
    }
    
    uploadFile() {
        const file = this.fileInput.files[0];
        if (!file) {
            alert('请选择一个txt文件！');
            return;
        }
        
        const reader = new FileReader();
        reader.onload = (e) => {
            const text = e.target.result;
            const newSentences = text.split('\n')
                .map(s => s.trim())
                .filter(s => s.length > 0);
            
            this.sentences = [...this.sentences, ...newSentences];
            this.updatePreview();
            this.fileInput.value = '';
            this.uploadBtn.style.display = 'none';
            
            this.playClickSound();
        };
        
        reader.readAsText(file);
    }
    
    updatePreview() {
        if (this.sentences.length === 0) {
            this.sentencesPreview.innerHTML = '<p class="empty-message">暂无短句，请添加短句后开始游戏</p>';
            this.startGameBtn.disabled = true;
        } else {
            const sentencesHtml = this.sentences.map((sentence, index) => 
                `<div class="sentence-item">${index + 1}. ${sentence}</div>`
            ).join('');
            
            this.sentencesPreview.innerHTML = sentencesHtml;
            this.startGameBtn.disabled = false;
        }
    }
    
    startGame() {
        if (this.sentences.length === 0) {
            alert('请先添加一些英语短句！');
            return;
        }
        
        this.currentSentenceIndex = 0;
        this.score = 0;
        this.shuffleSentences();
        
        this.uploadScreen.classList.remove('active');
        this.gameScreen.classList.add('active');
        
        this.updateGameInfo();
        this.loadCurrentQuestion();
        
        this.playClickSound();
    }
    
    shuffleSentences() {
        for (let i = this.sentences.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.sentences[i], this.sentences[j]] = [this.sentences[j], this.sentences[i]];
        }
    }
    
    loadCurrentQuestion() {
        if (this.currentSentenceIndex >= this.sentences.length) {
            this.finishGame();
            return;
        }
        
        const sentence = this.sentences[this.currentSentenceIndex];
        this.currentSentence = sentence.split(' ');
        this.selectedWords = [];
        this.currentWordIndex = 0;
        
        this.resetDinosaur();
        this.createBridge();
        this.createWordButtons();
        this.updateSentenceDisplay();
        this.updateGameInfo();
    }
    
    createBridge() {
        this.bridgeArea.innerHTML = '';
        
        for (let i = 0; i < this.currentSentence.length; i++) {
            const plank = document.createElement('div');
            plank.className = 'bridge-plank';
            plank.dataset.index = i;
            this.bridgeArea.appendChild(plank);
        }
    }
    
    createWordButtons() {
        this.wordSelection.innerHTML = '';
        
        // 创建打乱顺序的单词数组
        const shuffledWords = [...this.currentSentence];
        for (let i = shuffledWords.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffledWords[i], shuffledWords[j]] = [shuffledWords[j], shuffledWords[i]];
        }
        
        shuffledWords.forEach((word, index) => {
            const button = document.createElement('button');
            button.className = 'word-btn';
            button.textContent = word;
            button.dataset.word = word;
            button.addEventListener('click', () => this.selectWord(word, button));
            this.wordSelection.appendChild(button);
        });
    }
    
    selectWord(word, button) {
        this.playClickSound();
        
        const correctWord = this.currentSentence[this.currentWordIndex];
        
        if (word === correctWord) {
            // 正确选择
            button.classList.add('selected');
            button.disabled = true;
            
            this.selectedWords.push(word);
            this.placeBridgePlank(this.currentWordIndex);
            this.moveDinosaur();
            this.updateSentenceDisplay();
            
            this.currentWordIndex++;
            
            if (this.currentWordIndex >= this.currentSentence.length) {
                // 完成当前句子
                setTimeout(() => {
                    this.completeSentence();
                }, 500);
            }
        } else {
            // 错误选择
            button.classList.add('wrong');
            this.dinosaurFall();
        }
    }
    
    placeBridgePlank(index) {
        const plank = this.bridgeArea.children[index];
        if (plank) {
            plank.classList.add('placed');
        }
    }
    
    moveDinosaur() {
        const progress = (this.currentWordIndex / this.currentSentence.length);
        const maxLeft = window.innerWidth - 400; // 右侧悬崖位置
        const startLeft = 150; // 起始位置
        const newLeft = startLeft + (maxLeft - startLeft) * progress;
        
        this.dinosaur.style.left = newLeft + 'px';
    }
    
    resetDinosaur() {
        this.dinosaur.style.left = '150px';
        this.dinosaur.classList.remove('crying', 'celebrating');
        this.dinosaur.textContent = '🦕';
    }
    
    updateSentenceDisplay() {
        const displayText = this.selectedWords.join(' ') + 
            (this.selectedWords.length > 0 && this.currentWordIndex < this.currentSentence.length ? ' _' : '');
        this.sentenceDisplay.textContent = displayText || '点击单词开始造句...';
    }
    
    updateGameInfo() {
        this.currentQuestionSpan.textContent = this.currentSentenceIndex + 1;
        this.totalQuestionsSpan.textContent = this.sentences.length;
        this.scoreSpan.textContent = this.score;
    }
    
    completeSentence() {
        this.score += 10;
        this.updateGameInfo();
        
        this.dinosaur.classList.add('celebrating');
        this.dinosaur.textContent = '🦕';
        
        this.playSuccessSound();
        this.showSuccessModal();
    }
    
    dinosaurFall() {
        this.dinosaur.classList.add('crying');
        this.dinosaur.textContent = '😢';
        
        this.playFailureSound();
        
        setTimeout(() => {
            this.showFailureModal();
        }, 1000);
    }
    
    showSuccessModal() {
        this.successModal.classList.add('show');
    }
    
    showFailureModal() {
        this.failureModal.classList.add('show');
    }
    
    hideModals() {
        this.successModal.classList.remove('show');
        this.failureModal.classList.remove('show');
    }
    
    continueToNext() {
        this.hideModals();
        this.nextQuestion();
    }
    
    nextQuestion() {
        this.currentSentenceIndex++;
        this.loadCurrentQuestion();
    }
    
    retryQuestion() {
        this.hideModals();
        this.loadCurrentQuestion();
    }
    
    restartCurrentQuestion() {
        this.loadCurrentQuestion();
        this.playClickSound();
    }
    
    finishGame() {
        this.hideModals();
        alert(`游戏完成！\n总得分：${this.score}\n完成题目：${this.currentSentenceIndex}/${this.sentences.length}`);
        this.backToUpload();
    }
    
    backToUpload() {
        this.gameScreen.classList.remove('active');
        this.uploadScreen.classList.add('active');
        this.playClickSound();
    }
    
    // 音效播放方法
    playSuccessSound() {
        this.successSound.currentTime = 0;
        this.successSound.play().catch(e => console.log('音频播放失败:', e));
    }
    
    playFailureSound() {
        this.failureSound.currentTime = 0;
        this.failureSound.play().catch(e => console.log('音频播放失败:', e));
    }
    
    playClickSound() {
        this.clickSound.currentTime = 0;
        this.clickSound.play().catch(e => console.log('音频播放失败:', e));
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    new DinosaurBridgeGame();
});
