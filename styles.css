/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #87CEEB 0%, #98D8E8 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* 屏幕切换 */
.screen {
    display: none;
    width: 100%;
    min-height: 100vh;
}

.screen.active {
    display: block;
}

/* 上传界面样式 */
#upload-screen {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.upload-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    max-width: 800px;
    width: 100%;
}

.upload-container h1 {
    text-align: center;
    color: #2E8B57;
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.upload-container h2 {
    text-align: center;
    color: #666;
    font-size: 1.2em;
    margin-bottom: 30px;
}

.upload-section {
    margin-bottom: 30px;
}

.upload-section h3 {
    color: #2E8B57;
    margin-bottom: 20px;
    font-size: 1.3em;
}

.manual-input, .file-upload {
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 2px dashed #ddd;
}

.manual-input label, .file-upload label {
    display: block;
    margin-bottom: 10px;
    font-weight: bold;
    color: #555;
}

#manual-sentences {
    width: 100%;
    height: 120px;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    resize: vertical;
    margin-bottom: 10px;
}

#file-input {
    margin-bottom: 10px;
}

button {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.preview-section {
    border-top: 2px solid #eee;
    padding-top: 20px;
}

.sentences-list {
    max-height: 200px;
    overflow-y: auto;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.sentence-item {
    background: white;
    padding: 8px 12px;
    margin: 5px 0;
    border-radius: 5px;
    border-left: 4px solid #4CAF50;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.empty-message {
    text-align: center;
    color: #999;
    font-style: italic;
}

.start-btn {
    display: block;
    margin: 0 auto;
    padding: 15px 40px;
    font-size: 18px;
    background: linear-gradient(45deg, #FF6B6B, #FF5252);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.start-btn:hover {
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

/* 游戏界面样式 */
.game-container {
    width: 100%;
    height: 100vh;
    position: relative;
    overflow: hidden;
}

.game-header {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
}

.score-info {
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 20px;
    border-radius: 20px;
    font-weight: bold;
    color: #2E8B57;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.back-btn {
    background: linear-gradient(45deg, #FF9800, #F57C00);
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.game-area {
    width: 100%;
    height: 100vh;
    position: relative;
}

/* 背景样式 */
.background {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.sky {
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, #87CEEB 0%, #98D8E8 70%, #90EE90 100%);
}

.clouds {
    position: absolute;
    width: 100%;
    height: 100%;
}

.cloud {
    position: absolute;
    background: white;
    border-radius: 50px;
    opacity: 0.8;
}

.cloud:before, .cloud:after {
    content: '';
    position: absolute;
    background: white;
    border-radius: 50px;
}

.cloud1 {
    width: 80px;
    height: 40px;
    top: 20%;
    left: 10%;
    animation: float 20s infinite linear;
}

.cloud1:before {
    width: 50px;
    height: 50px;
    top: -25px;
    left: 10px;
}

.cloud1:after {
    width: 60px;
    height: 40px;
    top: -15px;
    right: 10px;
}

.cloud2 {
    width: 60px;
    height: 30px;
    top: 15%;
    right: 20%;
    animation: float 25s infinite linear;
}

.cloud2:before {
    width: 40px;
    height: 40px;
    top: -20px;
    left: 5px;
}

.cloud2:after {
    width: 50px;
    height: 30px;
    top: -10px;
    right: 5px;
}

.cloud3 {
    width: 70px;
    height: 35px;
    top: 25%;
    left: 60%;
    animation: float 30s infinite linear;
}

.cloud3:before {
    width: 45px;
    height: 45px;
    top: -22px;
    left: 8px;
}

.cloud3:after {
    width: 55px;
    height: 35px;
    top: -12px;
    right: 8px;
}

@keyframes float {
    0% { transform: translateX(-100px); }
    100% { transform: translateX(calc(100vw + 100px)); }
}

.cliff-left, .cliff-right {
    position: absolute;
    bottom: 0;
    width: 200px;
    height: 300px;
    background: linear-gradient(45deg, #8B4513, #A0522D);
    z-index: 2;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.3);
}

.cliff-left {
    left: 0;
    clip-path: polygon(0 100%, 100% 100%, 85% 0, 0 0);
}

.cliff-right {
    right: 0;
    clip-path: polygon(15% 0, 100% 0, 100% 100%, 0 100%);
}

/* 游戏元素样式 */
.game-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 10;
}

.dinosaur {
    position: absolute;
    bottom: 340px;
    left: 170px;
    font-size: 60px;
    transition: all 0.8s ease;
    z-index: 20;
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

.dinosaur.crying {
    animation: cry 0.5s ease-in-out;
}

.dinosaur.celebrating {
    animation: celebrate 1s ease-in-out;
}

@keyframes cry {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-5deg) scale(0.9); }
    75% { transform: rotate(5deg) scale(0.9); }
}

@keyframes celebrate {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.2) rotate(-10deg); }
    50% { transform: scale(1.1) rotate(10deg); }
    75% { transform: scale(1.2) rotate(-5deg); }
}

.bridge-area {
    position: absolute;
    bottom: 280px;
    left: 200px;
    right: 200px;
    height: 60px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    gap: 2px;
    z-index: 15;
}

.bridge-plank {
    height: 50px;
    background: linear-gradient(45deg, #8B4513, #A0522D);
    border-radius: 8px;
    border: 3px solid #654321;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    transition: all 0.5s ease;
    transform: translateY(-150px);
    opacity: 0.7;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    cursor: pointer;
    flex-shrink: 0;
}

.bridge-plank.placed {
    transform: translateY(0);
    opacity: 1;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.bridge-plank .word-text {
    color: #FFD700;
    font-size: 16px;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    text-align: center;
    padding: 5px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    min-width: 80%;
    word-wrap: break-word;
}

.bridge-plank.clickable:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 15px rgba(255, 215, 0, 0.4);
    background: linear-gradient(45deg, #A0522D, #CD853F);
}

.bridge-plank.selected {
    background: linear-gradient(45deg, #90EE90, #32CD32);
    border-color: #228B22;
    transform: translateY(0) scale(1.05);
}

.bridge-plank.wrong {
    background: linear-gradient(45deg, #FF6B6B, #FF4444);
    border-color: #CC0000;
    animation: shake 0.5s ease-in-out;
}

.word-selection {
    position: absolute;
    top: 100px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    max-width: 80%;
    z-index: 25;
}

.word-btn {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #8B4513;
    border: 3px solid #FF8C00;
    padding: 15px 25px;
    border-radius: 20px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.word-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6);
}

.word-btn.selected {
    background: linear-gradient(45deg, #90EE90, #32CD32);
    border-color: #228B22;
    color: white;
    transform: scale(0.9);
}

.word-btn.wrong {
    background: linear-gradient(45deg, #FF6B6B, #FF4444);
    border-color: #CC0000;
    color: white;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.current-sentence {
    position: absolute;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 20px;
    font-weight: bold;
    color: #2E8B57;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    border: 3px solid #4CAF50;
    z-index: 30;
}

.game-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 20px;
    z-index: 100;
}

/* 弹窗样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;
}

.modal-content h2 {
    margin-bottom: 20px;
    font-size: 2em;
}

.modal-content p {
    margin-bottom: 30px;
    font-size: 1.2em;
    color: #666;
}

.modal-content button {
    margin: 0 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .upload-container {
        padding: 20px;
        margin: 10px;
    }

    .upload-container h1 {
        font-size: 2em;
    }

    .word-btn {
        padding: 12px 20px;
        font-size: 16px;
    }

    .dinosaur {
        font-size: 40px;
        left: 120px;
        bottom: 320px;
    }

    .cliff-left, .cliff-right {
        width: 120px;
    }

    .bridge-area {
        left: 120px;
        right: 120px;
        bottom: 260px;
        height: 50px;
    }

    .bridge-plank {
        height: 40px;
    }

    .bridge-plank .word-text {
        font-size: 14px;
        padding: 3px;
    }

    .current-sentence {
        font-size: 14px;
        padding: 8px 16px;
        bottom: 80px;
    }
}
