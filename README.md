# 小恐龙过桥 - 英语连词成句游戏

一个有趣的英语学习游戏，帮助小朋友通过连词成句的方式学习英语，同时享受小恐龙过桥的冒险乐趣！

## 🎮 游戏特色

- **主题背景**：蓝天白云悬崖场景，营造过桥冒险氛围
- **互动学习**：通过点击单词组成正确句子来帮助小恐龙过桥
- **即时反馈**：正确选择时小恐龙前进，错误时小恐龙掉落并哭泣
- **成就感**：完成句子后小恐龙庆祝，配合鼓励音效
- **自定义内容**：支持手动输入或上传txt文件添加英语短句

## 🚀 快速开始

1. 打开 `index.html` 文件在浏览器中运行游戏
2. 在上传界面添加英语短句：
   - 手动输入：在文本框中输入短句，每行一个
   - 文件上传：准备一个txt文件，每行包含一个英语短句
3. 点击"开始游戏"进入游戏界面
4. 按照正确顺序点击单词，帮助小恐龙搭建桥梁过河

## 📁 文件结构

```
geminiauth/
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── script.js           # 游戏逻辑文件
├── sounds/             # 音频文件夹
│   ├── success.mp3     # 成功音效（需要添加）
│   ├── failure.mp3     # 失败音效（需要添加）
│   └── click.mp3       # 点击音效（需要添加）
└── README.md           # 说明文档
```

## 🎯 游戏玩法

### 数据上传界面
- **手动输入**：在文本区域输入英语短句，每行一个句子
- **文件上传**：选择包含英语短句的txt文件上传
- **短句预览**：查看已添加的所有短句
- **开始游戏**：确认短句库后开始游戏

### 游戏界面
- **背景场景**：蓝天白云悬崖，营造过桥氛围
- **小恐龙**：游戏主角，需要玩家帮助过桥
- **单词按钮**：打乱顺序的句子单词，点击组成正确句子
- **桥梁**：每选对一个单词，就会放置一块桥板
- **进度显示**：显示当前题目和得分

### 游戏规则
1. 屏幕上方显示打乱顺序的单词按钮
2. 按照正确的语法顺序点击单词
3. 每选对一个单词：
   - 单词变绿并禁用
   - 放置一块桥板
   - 小恐龙向前移动一步
4. 选错单词：
   - 单词变红并震动
   - 小恐龙掉落并哭泣
   - 显示重试选项
5. 完成整个句子：
   - 小恐龙庆祝动画
   - 播放成功音效
   - 获得分数奖励

## 🎵 音频文件

游戏需要以下音频文件（请自行添加到 `sounds/` 文件夹）：

- `success.mp3` - 成功完成句子时的庆祝音效
- `failure.mp3` - 选错单词时的失败音效（哭声）
- `click.mp3` - 点击按钮时的音效

## 🎨 自定义设置

### 添加新的英语短句
1. **手动添加**：在游戏开始界面的文本框中输入
2. **批量添加**：创建txt文件，每行一个句子，然后上传

### 示例短句文件格式
```
I love cats
She is happy
We play games
The sun is bright
Birds can fly
Dogs are friendly
Children like toys
Flowers smell good
```

## 🔧 技术特性

- **纯前端实现**：HTML + CSS + JavaScript，无需服务器
- **响应式设计**：支持不同屏幕尺寸
- **动画效果**：CSS动画和过渡效果
- **文件处理**：支持txt文件读取和解析
- **音频支持**：HTML5音频播放

## 🎓 教育价值

- **语法学习**：通过连词成句练习英语语法
- **词汇记忆**：重复接触单词加深记忆
- **逻辑思维**：培养句子结构的逻辑思维
- **成就感**：游戏化学习增加学习动力
- **即时反馈**：错误时立即提示，正确时及时鼓励

## 🌟 使用建议

1. **适合年龄**：6-12岁儿童英语学习
2. **句子难度**：建议从简单的3-5个单词句子开始
3. **学习进度**：每次游戏10-15个句子为宜
4. **重复练习**：可以重复相同的句子加深印象
5. **家长陪伴**：建议家长陪伴指导，解释语法规则

## 🐛 故障排除

- **音频无法播放**：检查浏览器是否支持音频自动播放
- **文件上传失败**：确保文件格式为txt且编码为UTF-8
- **游戏卡顿**：尝试刷新页面或使用现代浏览器
- **显示异常**：检查浏览器是否支持CSS Grid和Flexbox

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 基础游戏功能实现
- 支持手动输入和文件上传
- 完整的游戏界面和动画效果

---

享受学习英语的乐趣，帮助小恐龙安全过桥！🦕🌉
