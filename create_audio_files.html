<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频文件生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2E8B57;
            text-align: center;
        }
        .audio-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        .download-btn {
            background: #2196F3;
        }
        .download-btn:hover {
            background: #1976D2;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>小恐龙过桥游戏 - 音频文件生成器</h1>
        
        <div class="info">
            <strong>说明：</strong>这个页面可以帮助你生成游戏所需的音频文件。点击播放按钮试听，点击下载按钮保存到sounds文件夹。
        </div>
        
        <div class="audio-section">
            <h3>🎉 成功音效 (success.mp3)</h3>
            <p>当小恐龙成功完成句子时播放</p>
            <button onclick="playSuccess()">播放试听</button>
            <button class="download-btn" onclick="downloadSuccess()">下载音频</button>
        </div>
        
        <div class="audio-section">
            <h3>😢 失败音效 (failure.mp3)</h3>
            <p>当小恐龙选错单词掉落时播放</p>
            <button onclick="playFailure()">播放试听</button>
            <button class="download-btn" onclick="downloadFailure()">下载音频</button>
        </div>
        
        <div class="audio-section">
            <h3>🔊 点击音效 (click.mp3)</h3>
            <p>点击按钮时播放</p>
            <button onclick="playClick()">播放试听</button>
            <button class="download-btn" onclick="downloadClick()">下载音频</button>
        </div>
        
        <div class="info">
            <strong>使用方法：</strong>
            <ol>
                <li>点击各个"下载音频"按钮</li>
                <li>将下载的文件保存到游戏目录的sounds文件夹中</li>
                <li>确保文件名分别为：success.mp3, failure.mp3, click.mp3</li>
                <li>重新打开游戏即可听到音效</li>
            </ol>
        </div>
    </div>

    <script>
        // Web Audio API 音频生成
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        
        function createTone(frequency, duration, type = 'sine') {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
            oscillator.type = type;
            
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration);
        }
        
        function playSuccess() {
            // 播放成功音效 - 上升的音调
            createTone(523.25, 0.2); // C5
            setTimeout(() => createTone(659.25, 0.2), 200); // E5
            setTimeout(() => createTone(783.99, 0.3), 400); // G5
        }
        
        function playFailure() {
            // 播放失败音效 - 下降的音调
            createTone(440, 0.3); // A4
            setTimeout(() => createTone(369.99, 0.3), 300); // F#4
            setTimeout(() => createTone(293.66, 0.5), 600); // D4
        }
        
        function playClick() {
            // 播放点击音效 - 短促的音调
            createTone(800, 0.1, 'square');
        }
        
        function generateAudioBlob(frequencies, durations, type = 'sine') {
            const sampleRate = 44100;
            const totalDuration = durations.reduce((sum, dur) => sum + dur, 0);
            const buffer = audioContext.createBuffer(1, sampleRate * totalDuration, sampleRate);
            const data = buffer.getChannelData(0);
            
            let offset = 0;
            frequencies.forEach((freq, index) => {
                const duration = durations[index];
                const samples = sampleRate * duration;
                
                for (let i = 0; i < samples; i++) {
                    const t = i / sampleRate;
                    const amplitude = Math.exp(-t * 3) * 0.3; // 衰减
                    data[offset + i] = Math.sin(2 * Math.PI * freq * t) * amplitude;
                }
                offset += samples;
            });
            
            // 转换为WAV格式
            const wavBuffer = audioBufferToWav(buffer);
            return new Blob([wavBuffer], { type: 'audio/wav' });
        }
        
        function audioBufferToWav(buffer) {
            const length = buffer.length;
            const arrayBuffer = new ArrayBuffer(44 + length * 2);
            const view = new DataView(arrayBuffer);
            
            // WAV header
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };
            
            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, 1, true);
            view.setUint32(24, buffer.sampleRate, true);
            view.setUint32(28, buffer.sampleRate * 2, true);
            view.setUint16(32, 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length * 2, true);
            
            // PCM data
            const data = buffer.getChannelData(0);
            let offset = 44;
            for (let i = 0; i < length; i++) {
                const sample = Math.max(-1, Math.min(1, data[i]));
                view.setInt16(offset, sample * 0x7FFF, true);
                offset += 2;
            }
            
            return arrayBuffer;
        }
        
        function downloadAudio(blob, filename) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        function downloadSuccess() {
            const blob = generateAudioBlob([523.25, 659.25, 783.99], [0.2, 0.2, 0.3]);
            downloadAudio(blob, 'success.wav');
        }
        
        function downloadFailure() {
            const blob = generateAudioBlob([440, 369.99, 293.66], [0.3, 0.3, 0.5]);
            downloadAudio(blob, 'failure.wav');
        }
        
        function downloadClick() {
            const blob = generateAudioBlob([800], [0.1], 'square');
            downloadAudio(blob, 'click.wav');
        }
    </script>
</body>
</html>
